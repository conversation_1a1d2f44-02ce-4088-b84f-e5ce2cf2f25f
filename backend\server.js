
const express = require("express");
const fs = require("fs");
const cors = require("cors");
const path = require("path");

const app = express();
const PORT = 5000;

app.use(cors());
app.use(express.json());

const dataFile = path.join(__dirname, "data.json");

function loadData() {
  if (!fs.existsSync(dataFile)) {
    fs.writeFileSync(dataFile, JSON.stringify({ fitness: [], nutrition: [], wellness: [] }, null, 2));
  }
  return JSON.parse(fs.readFileSync(dataFile));
}

function saveData(data) {
  fs.writeFileSync(dataFile, JSON.stringify(data, null, 2));
}

app.get("/api/test", (req, res) => {
  res.json({ message: "✅ Backend connected successfully!" });
});

app.get("/api/:section", (req, res) => {
  const section = req.params.section;
  const data = loadData();
  if (!data[section]) return res.status(404).json({ error: "Section not found" });
  res.json(data[section]);
});

app.post("/api/:section", (req, res) => {
  const section = req.params.section;
  const entry = req.body;
  const data = loadData();
  if (!data[section]) return res.status(404).json({ error: "Section not found" });
  data[section].push({ ...entry, date: new Date().toISOString() });
  saveData(data);
  res.json({ success: true, data: data[section] });
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
});
